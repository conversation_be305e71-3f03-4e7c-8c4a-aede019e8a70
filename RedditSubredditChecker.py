import praw
import time
import logging
from datetime import datetime, timezone

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Reddit API Setup ---
logging.info("Initializing Reddit API...")
reddit = praw.Reddit(
    client_id='5By3t8CS5mX38dsL9oT49g',
    client_secret='e7nfwjDAKCDmIkLarUJfDZcZpJpRVg',
    user_agent='engagement_tracker_script'
)
logging.info("Reddit API initialized.")

# --- Load Subreddits from cleaned.txt ---
try:
    with open('cleaned.txt', 'r') as f:
        subreddits = [line.strip() for line in f if line.strip()]
    logging.info(f"Loaded {len(subreddits)} subreddits from cleaned.txt.")
except FileNotFoundError:
    logging.error("cleaned.txt not found. Please make sure the file exists.")
    subreddits = []
    
def get_subreddit_metrics(subreddit_name, post_limit=100):
    logging.info(f"Fetching metrics for r/{subreddit_name}...")
    try:
        subreddit = reddit.subreddit(subreddit_name)
        posts = list(subreddit.new(limit=post_limit))
        logging.info(f"Fetched {len(posts)} posts from r/{subreddit_name}.")

        if len(posts) < 10:
            logging.warning(f"Too few posts in r/{subreddit_name}, skipping.")
            return None

        now = datetime.now(timezone.utc)
        timestamps = [(now - datetime.fromtimestamp(p.created_utc, tz=timezone.utc)).total_seconds() / 3600 for p in posts]
        upvotes = [p.score for p in posts]

        poster_ages = []
        link_karmas = []
        comment_karmas = []

        for post in posts:
            try:
                author = post.author
                if author is not None:
                    created_utc = datetime.fromtimestamp(author.created_utc, tz=timezone.utc)
                    account_age = (now - created_utc).total_seconds() / (3600 * 24)  # days
                    poster_ages.append(account_age)

                    link_karmas.append(author.link_karma)
                    comment_karmas.append(author.comment_karma)
                else:
                    logging.debug(f"Post with no author in r/{subreddit_name}.")
            except Exception as e:
                logging.warning(f"Error fetching user info in r/{subreddit_name}: {e}")

        if not poster_ages or not link_karmas or not comment_karmas:
            logging.warning(f"Incomplete poster data in r/{subreddit_name}.")
            return None

        total_upvotes = sum(upvotes)
        post_density = len(posts) / (max(timestamps) - min(timestamps) + 1e-6)
        avg_upvotes_per_post = total_upvotes / len(posts)

        latest_10 = posts[:10]
        l10_upvotes = [p.score for p in latest_10]
        l10_times = [(now - datetime.fromtimestamp(p.created_utc, tz=timezone.utc)).total_seconds() / 3600 for p in latest_10]
        l10_timespan = max(l10_times) - min(l10_times) + 1e-6
        l10_avg_upvotes = sum(l10_upvotes) / 10
        l10_score = l10_avg_upvotes / l10_timespan

        min_link_karma = min(link_karmas)
        min_comment_karma = min(comment_karmas)
        min_account_age = min(poster_ages)

        logging.info(f"r/{subreddit_name} | Score: {l10_score:.2f}, Min Link: {min_link_karma}, Min Comment: {min_comment_karma}, Min Age: {min_account_age:.1f}d")

        return {
            'subreddit': subreddit_name,
            'post_density': post_density,
            'total_upvotes': total_upvotes,
            'avg_upvotes': avg_upvotes_per_post,
            'l10_timespan': l10_timespan,
            'l10_avg_upvotes': l10_avg_upvotes,
            'score': l10_score,
            'min_account_age_days': min_account_age,
            'min_link_karma': min_link_karma,
            'min_comment_karma': min_comment_karma
        }

    except Exception as e:
        logging.error(f"Failed to fetch metrics for r/{subreddit_name}: {e}")
        return {'subreddit': subreddit_name, 'error': str(e)}

# --- Run Analysis ---
logging.info("Starting subreddit analysis...")
results = []

for sub in subreddits:
    result = get_subreddit_metrics(sub)
    if result:
        results.append(result)

results = [r for r in results if 'error' not in r]
ranked = sorted(results, key=lambda x: x['score'], reverse=True)

logging.info("Top Subreddits by Score:")
for r in ranked:
    logging.info(
        f"{r['subreddit']} | Score: {r['score']:.2f} | Min Post Karma: {r['min_link_karma']} | Min Comment Karma: {r['min_comment_karma']} | Youngest Account: {r['min_account_age_days']:.1f} days"
    )
