
input_file = "input.txt"
output_file = "cleaned.txt"

with open(input_file, "r") as f:
    lines = f.readlines()

# Process lines
cleaned = []
for line in lines:
    line = line.strip()
    # Remove /r from the beginning if it exists
    if line.startswith("/r"):
        line = line[2:]  # Remove the first 2 characters ("/r")
    # Remove spaces and dots
    line = line.replace(" ", "").replace(".", "")
    if line:
        cleaned.append(line)

# Save the cleaned output
with open(output_file, "w") as f:
    for entry in cleaned:
        f.write(entry + "\n")

# Show number of entries
print(f"Total entries: {len(cleaned)}")
